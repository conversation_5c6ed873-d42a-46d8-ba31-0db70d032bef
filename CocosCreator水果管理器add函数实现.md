# Cocos Creator水果管理器add函数实现

## Core Features

- 水果节点创建

- 位置设置

- 重力启用

- 碰撞检测

- 预制体实例化

## Tech Stack

{
  "Web": {
    "arch": "html",
    "component": null
  }
}

## Design

为FruitMgr.js添加add函数，实现水果合成逻辑。函数接收level和pos参数，创建指定等级的水果节点，设置位置，启用重力和碰撞检测。

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 分析现有FruitMgr.js文件结构和已实现功能

[X] 设计add函数的参数接口和功能逻辑

[X] 实现水果节点创建和预制体实例化

[X] 添加水果位置设置和物理属性配置

[X] 集成重力系统和碰撞检测功能

[X] 测试add函数与现有代码的兼容性
