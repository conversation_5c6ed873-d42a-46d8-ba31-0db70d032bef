let resMgr = require("ResMgr");

cc.Class({
    extends: cc.Component,

    properties: {
        //accel:0
    },

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        //直接往脚本插入speed的属性，这样既可以保证有这么一个属性又可以确保这个属性不会被意外改掉初始值
       
        // this.initWithData("6西红柿");
        this.isAdd =false;
    },
    initWithData(fruitData) {
        // 插入一个属性存储水果的等级
        this.level = fruitData.level;
        let img = resMgr.getImg(fruitData.img);
        let sprite = this.node.getComponent(cc.Sprite);
        sprite.spriteFrame = img;
        // 设置水果碰撞器的范围
        // 停止章到半径
        let radius = this.node.width / 2;
        let circleCollider = this.node.getComponent(cc.PhysicsCircleCollider);
        circleCollider.offset.y = radius;
        circleCollider.radius = radius;
    },
    start () {

    },

    update (dt) {
    
    
       
    },
    onBeginContact: function (contact, selfCollider, otherCollider) {
        if (otherCollider.node.group === "fruit") {

            let otherFruitJs = otherCollider.node.getComponent("Fruit");
            let selfFruitJs = selfCollider.node.getComponent("Fruit");
            if(otherFruitJs.isAdd||selfFruitJs.isAdd) {
                return;}

            if (selfFruitJs.level === otherFruitJs.level) {
                // 立即标记两个水果为已处理，防止重复触发
                otherFruitJs.isAdd = true;
                selfFruitJs.isAdd = true;

                let fruitMgrJs = this.node.parent.getComponent("FruitMgr");
                fruitMgrJs.add(otherFruitJs.level + 1, selfCollider.node.position);

                // 移除两个碰撞的水果
                otherCollider.node.removeFromParent();
                selfCollider.node.removeFromParent();
            }
        }
      }
    

});
