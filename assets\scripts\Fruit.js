let resMgr = require("ResMgr");

cc.Class({
    extends: cc.Component,

    properties: {
        //accel:0
    },

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        //直接往脚本插入speed的属性，这样既可以保证有这么一个属性又可以确保这个属性不会被意外改掉初始值
       
        // this.initWithData("6西红柿");
    },
    initWithData(fruitData) {
        this.level = fruitData.level;


        let img = resMgr.getImg(fruitData.img);
        let sprite = this.getComponent(cc.Sprite);
        sprite.spriteFrame = img;
    },
    start () {

    },

    update (dt) {
    
    
       
    },
      onBeginContact: function (contact, selfCollider, otherCollider) {
        if (otherCollider.node.group === "fruit") {
       
            let otherFruitJs = otherCollider.node.getComponent("Fruit");
            let selfFruitJs = selfCollider.node.getComponent("Fruit");
            if(otherFruitJs.isAdd||selfFruitJs.isAdd) {
                return;}

            if (selfFruitJs.level === otherFruitJs.level) {
          let fruitMgrJs = this.node.parent.getComponent("FruitMgr");
          let result = fruitMgrJs.add(otherFruitJs.level +1,selfCollider.node.position)


            if (result) {
                otherCollider.node.removeFromParent();
                selfCollider.node.removeFromParent();
            }


            
        }
      }
    }
});
