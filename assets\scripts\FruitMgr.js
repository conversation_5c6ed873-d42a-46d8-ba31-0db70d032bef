
cc.Class({
    extends: cc.Component,

    properties: {
     fruitPrefab: cc.Prefab,
      thouchLayer: cc.Node,
      fruitJson:cc.JsonAsset,
    },

    // LIFE-CYCLE CALLBACKS:

     onLoad () {

        this.newfruit =null;

        this.maxLevel = 3;

        this.createFruit();
        this.thouchLayer.on(cc.Node.EventType.TOUCH_START, this.ontouchStart, this);
        this.thouchLayer.on(cc.Node.EventType.TOUCH_MOVE, this.ontouchMove, this);
        this.thouchLayer.on(cc.Node.EventType.TOUCH_END, this.ontouchEnd, this);

        console.log(this.fruitJson.json);
     },
    
    start () {

    },

    // update (dt) {},

    createFruit() {
        let level = Math.floor(Math.random() * this.maxLevel+ 1- 1) + 1;

        let fruitData = this.fruitJson.json[level-1];
            let fruitN = cc.instantiate(this.fruitPrefab);
            fruitN.parent = this.node;
            
            let fruitScript = fruitN.getComponent("Fruit");
            fruitScript.initWithData(fruitData);
            this.newfruit =fruitN;
    },
    ontouchStart (event) {
              if (!this.newfruit) {
               return;
            }
            let worldPos = event.getLocation();
            let clickPos = this.node.convertToNodeSpaceAR(worldPos);
            this.newfruit.x= clickPos.x;
   
    },
    ontouchMove (event) {
             if (!this.newfruit) {
               return;
            }
            let worldPos = event.getLocation();
            let clickPos = this.node.convertToNodeSpaceAR(worldPos);
            this.newfruit.x= clickPos.x;
    },
    ontouchEnd () {
             if (!this.newfruit) {
               return;
            }
            let rigidBody = this.newfruit.getComponent(cc.RigidBody);
            rigidBody.gravityScale = 1;

             this.newfruit =null;
            //  this.scheduleOnce(() => {
            //     this.createFruit();}, 0.5);
            this.scheduleOnce(this.createFruit, 0.5);

    },
    // 合成逻辑
    add(level, pos) {
        // 创建指定等级的水果
        let fruitDt = this.fruitJson.json[level - 1];
        let fruitN = cc.instantiate(this.fruitPrefab);
        fruitN.parent = this.node;
        let fruitJs = fruitN.getComponent("Fruit");
        fruitJs.initWithData(fruitDt);

        // 设置水果位置
        fruitN.x = pos.x;
        fruitN.y = pos.y;
        
        // 开启合成的水果的重力
        let rigidBody = fruitN.getComponent(cc.RigidBody);
        rigidBody.gravityScale = 1;
        let circleCollider = fruitN.getComponent(cc.PhysicsCircleCollider);
        circleCollider.apply();
    },

    onDestroy() {
            this.thouchLayer.off(cc.Node.EventType.TOUCH_START, this.ontouchStart, this);
            this.thouchLayer.off(cc.Node.EventType.TOUCH_MOVE, this.ontouchMove, this);
            this.thouchLayer.off(cc.Node.EventType.TOUCH_END, this.ontouchEnd, this);
    }
});
