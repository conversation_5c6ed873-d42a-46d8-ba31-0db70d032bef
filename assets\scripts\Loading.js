let resMgr = require("ResMgr");
cc.Class({
    extends: cc.Component,

    properties: {
        progressBar: cc.ProgressBar,
    },

    // LIFE-CYCLE CALLBACKS:

     onLoad () {

        cc.resources.loadDir("",  (finish, total,item)=> {
            this.progressBar.progress = finish / total;
        },(error, assets) => {
            if (error) {
                cc.error(error);
                

                console.error("加载资源失败"+error);
                return;
            }
            // 加载完成后，跳转到菜单场景
            // this.loadMenuScene();
            console.log("加载资源成功", assets);

            console.log(resMgr);
            for(let asset of assets) {
                if(asset instanceof cc.SpriteFrame) {
                    resMgr.addImg(asset);
                }
                else if(asset instanceof cc.AudioClip) {
                    resMgr.addAudio(asset);
                    console.log("加载音频", asset);
                }
            }
            console.log("resMgr", resMgr);


             cc.director.loadScene("Game");
        });

       
     },

    start () {

    },

    // update (dt) {},
});
