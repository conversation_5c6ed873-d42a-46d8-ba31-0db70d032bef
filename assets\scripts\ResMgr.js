class ResMgr {
    arrImgs = [];
    arrAudio = [];

    addImg(img) {
        if (!img || !(img instanceof cc.SpriteFrame)) {
            return;
        }
        this.arrImgs.push(img);
    }

    getImg(imgname) {
        for (let img of this.arrImgs) {
            if (img.name === imgname) {
                return img;
            }
        }
        return null;
    }

    addAudio(audio) {
        if (!audio || !(audio instanceof cc.AudioClip)) {
            return;
        }
        this.arrAudio.push(audio);
    }

    getAudio(audioname) {
        for (let audio of this.arrAudio) {
            if (audio.name === audioname) {
                return audio;
            }
        }
        return null;
    }
}

let resMgr = new ResMgr();

module.exports = resMgr;